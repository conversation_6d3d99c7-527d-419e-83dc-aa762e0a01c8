import { Link, Outlet } from 'react-router-dom';
import { toAbsoluteUrl } from '@/lib/helpers';
import { Card, CardContent } from '@/components/ui/card';

export function BrandedLayout() {
  return (
    <>
      {/* <style>
        {`
          .branded-bg {
            background-image: url('${toAbsoluteUrl('/media/images/2600x1600/1.png')}');
          }
          .dark .branded-bg {
            background-image: url('${toAbsoluteUrl('/media/images/2600x1600/1-dark.png')}');
          }
        `}
      </style> */}
      <div className="flex flex-col lg:flex-row  grow">
    

        <div className=" flex flex-col justify-center items-center  w-1/3  order-1 lg:order-2 bg-top xxl:bg-center xl:bg-cover bg-no-repeat bg-music-background">

          <div className="flex gap-8 flex-col p-8 lg:p-12 gap-12">
          <Link to="/" className="flex justify-center items-center">
              <img
                src={toAbsoluteUrl('/media/swarasacademy/logo.png')}
                className="h-[80px] max-w-none"
                alt=""
              />
            </Link>
            <div className="flex flex-col gap-3">
              <h3 className="text-3xl text-center font-bold  text-music-background-light">
              Welcome to Swaras Academy
              </h3>
                <p className="text-xl text-music-light mt-2 font-medium text-center text-music-background-light">Swaras is a spiritual initiative founded by Swarupa Daamodar Das</p>
            </div>
            <div className="flex justify-center items-center">
              <img
                src={toAbsoluteUrl('https://swarasacademy.com/wp-content/uploads/2025/02/img1.png')}
                className=" h-[400px] max-w-none"
                alt=""
              />
            </div>

          </div>
        </div>
        
        <div className="w-2/3 flex justify-center items-center p-8 lg:p-10 order-2 lg:order-2">
          <Card className="w-full max-w-[600px] ">
            <CardContent className="p-6 bg-music-background-orange ">
              <Outlet />
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}
