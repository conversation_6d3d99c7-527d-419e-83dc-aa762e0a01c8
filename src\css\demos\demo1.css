@layer components {
  /* Variables */
  .demo1 {
    --sidebar-transition-duration: 0.3s;
    --sidebar-transition-timing: ease;
    --sidebar-width: 280px;
    --sidebar-width-collapse: 80px;
    --sidebar-default-width: 280px;
    --header-height: 70px;
  }

  @media (max-width: theme(--breakpoint-lg)) {
    .demo1 {
      --sidebar-width: 280px;
      --header-height: 60px;
    }
  }

  /* Base */
  .demo1 .header {
    height: var(--header-height);
  }

  .demo1 .sidebar {
    width: var(--sidebar-width);
  }

  .demo1.header-fixed .wrapper {
    padding-top: var(--header-height);
  }

  /* Desktop mode */
  @media (min-width: theme(--breakpoint-lg)) {
    .demo1 .sidebar {
      width: var(--sidebar-width);
      transition: width var(--sidebar-transition-duration)
        var(--sidebar-transition-timing);
    }

    .demo1 .sidebar .sidebar-header {
      height: var(--header-height);
    }

    .demo1 .sidebar .sidebar-wrapper {
      width: var(--sidebar-default-width);
    }

    .demo1 .sidebar .sidebar-logo {
      width: var(--sidebar-default-width);
    }

    .demo1 .sidebar .small-logo {
      display: none;
    }

    .demo1.sidebar-fixed .wrapper {
      padding-inline-start: var(--sidebar-width) !important;
    }

    .demo1.sidebar-fixed.header-fixed .header {
      inset-inline-start: var(--sidebar-width) !important;
    }

    .demo1.sidebar-fixed.header-fixed .wrapper {
      padding-top: var(--header-height);
    }

    .demo1.sidebar-collapse {
      --sidebar-width: var(--sidebar-width-collapse);
    }

    .demo1.sidebar-collapse .sidebar {
      transition: width var(--sidebar-transition-duration)
        var(--sidebar-transition-timing);
    }

    .demo1.sidebar-collapse .sidebar:hover {
      width: var(--sidebar-default-width);
    }

    .demo1.sidebar-collapse .sidebar:not(:hover) .default-logo {
      display: none;
    }

    .demo1.sidebar-collapse .sidebar:not(:hover) .small-logo {
      display: flex;
    }

    .demo1.sidebar-collapse
      .sidebar:not(:hover)
      [data-slot='accordion-menu-title'],
    .demo1.sidebar-collapse .sidebar:not(:hover) [data-slot='badge'],
    .demo1.sidebar-collapse
      .sidebar:not(:hover)
      [data-slot='accordion-menu-sub-indicator'] {
      display: none !important;
      transition: none !important;
      animation: none !important;
    }

    .demo1.sidebar-collapse
      .sidebar:not(:hover)
      [data-slot='accordion-menu-sub-content'] {
      visibility: hidden;
      position: absolute;
      height: 0;
      width: 0;
    }

    .demo1.sidebar-collapse
      .sidebar:not(:hover)
      [data-slot='accordion-menu-label'] {
      visibility: hidden;
      position: relative;
    }

    .demo1.sidebar-collapse
      .sidebar:not(:hover)
      [data-slot='accordion-menu-label']::before {
      content: '...';
      color: currentColor;
      font-size: inherit;
      position: absolute;
      visibility: visible;
      display: inline-block;
      bottom: 50%;
      inset-inline-start: 0;
      margin-inline-start: -0.15rem;
      top: 0.25rem;
      transform: translateX(100%);
    }
  }

  .demo1.layout-initialized .wrapper {
    transition: padding-inline-start var(--sidebar-transition-duration)
      var(--sidebar-transition-timing);
  }

  .demo1.layout-initialized .header {
    transition: inset-inline-start var(--sidebar-transition-duration)
      var(--sidebar-transition-timing);
  }
}
