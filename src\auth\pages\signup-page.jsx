import { useState } from 'react';
import { useAuth } from '@/auth/context/auth-context';
import { zodResolver } from '@hookform/resolvers/zod';
import { AlertCircle, Check, Eye, EyeOff } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';
import { Alert, AlertIcon, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Spinner } from '@/components/ui/spinners';
import { getSignupSchema } from '../forms/signup-schema';
import React from 'react';

export function SignUpPage() {
  const navigate = useNavigate();
  const { register } = useAuth();
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [otpProcessing, setOtpProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const [isEmailValid, setIsEmailValid] = useState(false);
  const [otpError, setOtpError] = useState(null);
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [isOtpEntered, setIsOtpEntered] = useState(false);

  const form = useForm({
    resolver: zodResolver(getSignupSchema()),
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      firstName: '',
      lastName: '',
      terms: false,
      otp: '',
    },
  });

  // Add email and OTP validation watchers
  const email = form.watch('email');
  const otp = form.watch('otp');
  
  // Update email validation state whenever email changes
  React.useEffect(() => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    setIsEmailValid(emailRegex.test(email));
  }, [email]);

  // Countdown timer effect
  React.useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    } else if (countdown === 0 && isOtpSent) {
      setIsOtpSent(false);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [countdown, isOtpSent]);

  // Watch OTP field for changes
  React.useEffect(() => {
    if (otp && otp.length > 0) {
      setIsOtpEntered(true);
    } else {
      setIsOtpEntered(false);
    }
  }, [otp]);

  async function onSubmit(values) {
    try {
      setIsProcessing(true);
      setError(null);
      setOtpError(null);

      // Validate OTP before proceeding with registration
      const otp = values.otp;
      if (!otp || otp.length !== 6 || !/^\d+$/.test(otp)) {
        setOtpError('Please enter a valid 6-digit OTP');
        setIsProcessing(false);
        return;
      }

      // Register the user with Supabase
      await register(
        values.email,
        values.password,
        values.confirmPassword,
        values.firstName,
        values.lastName,
      );

      // Set success message and metadata
      setSuccessMessage(
        'Registration successful! Please check your email to confirm your account.',
      );

      // After successful registration, you might want to update the user profile
      // with additional metadata (firstName, lastName, etc.)

      // Optionally redirect to login page after a delay
      setTimeout(() => {
        navigate('/auth/signin');
      }, 3000);
    } catch (err) {
      console.error('Registration error:', err);
      setError(
        err instanceof Error
          ? err.message
          : 'An unexpected error occurred during registration. Please try again.',
      );
    } finally {
      setIsProcessing(false);
    }
  }

  async function onOtpSubmit() {
    try {
      setOtpProcessing(true);
      setOtpError(null);
      
      if (countdown === 0) {
        // Generate and send OTP
        // Here you would typically send OTP to the user's email/phone
        setIsOtpSent(true);
        setCountdown(60); // Start 60 second countdown
      } else {
        // Verify OTP
        if (!otp || otp.length !== 6 || !/^\d+$/.test(otp)) {
          setOtpError('Please enter a valid 6-digit OTP');
          return;
        }
        // Here you would typically verify the OTP with your backend
      }
      
    } catch (err) {
      console.error('OTP error:', err);
      setOtpError(
        err instanceof Error
          ? err.message 
          : 'An unexpected error occurred. Please try again.'
      );
    } finally {
      setOtpProcessing(false);
    }
  }

  return (
    <Form {...form}>
      <form 
        onSubmit={form.handleSubmit(onSubmit)}
        className="block w-full space-y-5"
      >
        <div className="text-center space-y-1 pb-3 border-b border-music-light">
          <h1 className="text-3xl font-semibold text-music-light tracking-tight">Sign Up</h1>
          <p className="text-lg  text-music-light">
            Create your account to get started
          </p>
        </div>

        {error && (
          <Alert
            variant="music"
            appearance="light"
            onClose={() => setError(null)}
          >
            <AlertIcon>
              <AlertCircle />
            </AlertIcon>
            <AlertTitle>{error}</AlertTitle>
          </Alert>
        )}

        {otpError && (
          <Alert
            variant="music"
            appearance="light"
            onClose={() => setOtpError(null)}
          >
            <AlertIcon>
              <AlertCircle />
            </AlertIcon>
            <AlertTitle>{otpError}</AlertTitle>
          </Alert>
        )}

        {successMessage && (
          <Alert appearance="light" onClose={() => setSuccessMessage(null)}>
            <AlertIcon>
              <Check />
            </AlertIcon>
            <AlertTitle>{successMessage}</AlertTitle>
          </Alert>
        )}

        <FormField
          control={form.control}
          name="firstName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Your Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter your name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* <FormField
          control={form.control}
          name="lastName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Last Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter your last name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        /> */}
        <div className="flex flex-row justify-between items-start gap-2">
          <div className='w-3/5'> 
          <FormField 
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Your email address"
                    type="email"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          </div>
          <div className="flex  w-2/5 gap-2 items-end">
            <div className='w-full flex flex-row gap-2'>
          <FormField
            control={form.control}
            name="otp"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Enter OTP</FormLabel>
                <FormControl>
                  <Input
                    placeholder="otp"
                    type="number"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          </div>
          <div className='w-full'>
          <Button 
            type="button" 
            variant="music" 
            mode="imput" 
            onClick={onOtpSubmit} 
            className="w-full" 
            disabled={otpProcessing || !isEmailValid || (countdown > 0 && !otp)}
          >
          {otpProcessing ? (
            <span className="flex items-center gap-2">
              <Spinner className="h-4 w-4 animate-spin" /> 
              {countdown > 0 ? 'Verifying OTP...' : 'Generating OTP...'}
            </span>
          ) : countdown > 0 ? (
            otp ? 'Verify OTP' : `Resend OTP in ${countdown}s`
          ) : (
            'Generate OTP'
          )}
        </Button>
          </div>
          </div>
        </div>

        <FormField 
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Your Phone Number"
                    type="phone"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

        

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <div className="relative">
                <Input
                  placeholder="Create a password"
                  type={passwordVisible ? 'text' : 'password'}
                  {...field}
                />

                <Button
                  type="button"
                  variant="ghost"
                  mode="icon"
                  onClick={() => setPasswordVisible(!passwordVisible)}
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                >
                  {passwordVisible ? (
                    <EyeOff className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  )}
                </Button>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Confirm Password</FormLabel>
              <div className="relative">
                <Input
                  placeholder="Confirm your password"
                  type={confirmPasswordVisible ? 'text' : 'password'}
                  {...field}
                />

                <Button
                  type="button"
                  variant="ghost"
                  mode="icon"
                  onClick={() =>
                    setConfirmPasswordVisible(!confirmPasswordVisible)
                  }
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                >
                  {confirmPasswordVisible ? (
                    <EyeOff className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  )}
                </Button>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="terms"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-0.5 space-y-0 rounded-md">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel className="text-sm text-music-light">
                  I agree to the and{' '}
                  <Link
                    to="#"
                    className="text-sm font-semibold text-music-background hover:text-music-light "
                  >
                    Privacy Policy
                  </Link>
                </FormLabel>
                <FormMessage />
              </div>
            </FormItem>
          )}
        />

        <Button type="submit" variant="music" mode="imput" className="w-full" disabled={isProcessing}>
          {isProcessing ? (
            <span className="flex items-center gap-2">
              <Spinner className="h-4 w-4 animate-spin" /> Creating account...
            </span>
          ) : (
            'Create Account'
          )}
        </Button>

        <div className="text-center text-sm text-music-light">
          Already have an account?{' '}
          <Link
            to="/auth/signin"
            className="text-sm font-semibold text-music-background hover:text-music-light"
          >
            Sign In
          </Link>
        </div>
      </form>
    </Form>
  );
}
