import { useState } from 'react';
import { DropdownMenu5 } from '@/partials/dropdown-menu/dropdown-menu-5';
import { Check, EllipsisVertical, Plus } from 'lucide-react';
import { Link } from 'react-router-dom';
import { toAbsoluteUrl } from '@/lib/helpers';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

const Connections = ({ title }) => {
  const [items, setItems] = useState([
    {
      avatar: '300-3.png',
      name: '<PERSON>',
      connections: 6,
      connected: false,
    },
    {
      avatar: '300-1.png',
      name: '<PERSON>',
      connections: 29,
      connected: true,
    },
    {
      avatar: '300-14.png',
      name: '<PERSON>',
      connections: 34,
      connected: false,
    },
    {
      avatar: '300-7.png',
      name: '<PERSON><PERSON>',
      connections: 1,
      connected: true,
    },
  ]);

  const toggleConnection = (index) => {
    setItems((prevItems) =>
      prevItems.map((item, i) =>
        i === index ? { ...item, connected: !item.connected } : item,
      ),
    );
  };

  const renderItem = (item, index) => {
    return (
      <div key={index} className="flex items-center gap-2">
        <div className="flex items-center grow gap-2.5">
          <img
            src={toAbsoluteUrl(`/media/avatars/${item.avatar}`)}
            className="rounded-full size-9 shrink-0"
            alt="image"
          />

          <div className="flex flex-col">
            <Link
              to="#"
              className="text-sm font-medium text-mono hover:text-primary-active mb-px"
            >
              {item.name}
            </Link>
            <span className="text-xs text-secondary-foreground">
              {item.connections} connections
            </span>
          </div>
        </div>
        <Button
          className={`rounded-full ${
            item.connected
              ? 'bg-blue-500 text-white'
              : 'bg-blue-50 border border-blue-300 text-blue-600 hover:text-white hover:bg-blue-500 dark:border-blue-950 dark:bg-blue-950/30'
          }`}
          size="sm"
          mode="icon"
          variant={item.connected ? 'primary' : 'outline'}
          onClick={() => toggleConnection(index)}
        >
          {item.connected ? <Check size={18} /> : <Plus size={18} />}
        </Button>
      </div>
    );
  };

  return (
    <Card>
      <CardHeader className="gap-2">
        <CardTitle className="card-title">{title}</CardTitle>
        <DropdownMenu5
          trigger={
            <Button variant="ghost" mode="icon">
              <EllipsisVertical />
            </Button>
          }
        />
      </CardHeader>
      <CardContent className="card-body">
        <div className="flex flex-col gap-2 lg:gap-5">
          {items.map((item, index) => {
            return renderItem(item, index);
          })}
        </div>
      </CardContent>
      <CardFooter className="justify-center">
        <Button mode="link" underlined="dashed" asChild>
          <Link to="/public-profile/network">All Contributors</Link>
        </Button>
      </CardFooter>
    </Card>
  );
};

export { Connections };
