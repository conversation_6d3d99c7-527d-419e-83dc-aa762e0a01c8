import React, { useState } from 'react';
import DOMPurify from 'dompurify';
import { countries } from '@/lib/Country';


const Course = () => {
  const [bannerPreview, setBannerPreview] = useState(null);
  const [featurePreview, setFeaturePreview] = useState(null);
  const [description, setDescription] = useState('');
  const [activeTab, setActiveTab] = useState('code'); // 'code' or 'preview'
  const [packageName, setPackageName] = useState('');
  const [duration, setDuration] = useState('');
  const [defaultPrice, setDefaultPrice] = useState('');
  const [selectedCountry, setSelectedCountry] = useState('');
  const [countryCurrencyValue, setCountryCurrencyValue] = useState('');
  const [countryPrices, setCountryPrices] = useState({});

  const handleAddCountryPrice = () => {
    if (selectedCountry && countryCurrencyValue !== '') {
      setCountryPrices((prev) => ({
        ...prev,
        [selectedCountry]: countryCurrencyValue,
      }));
      setSelectedCountry('');
      setCountryCurrencyValue('');
    }
  };

  const handleImageChange = (e, setPreview) => {
    const file = e.target.files[0];
    if (file) {
      setPreview(URL.createObjectURL(file));
    }
  };

  const removeImage = (setPreview, inputId) => {
    setPreview(null);
    document.getElementById(inputId).value = '';
  };

  // form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    const finalPrices = {};
    Object.keys(countryPrices).forEach((countryName) => {
      finalPrices[countryName] = countryPrices[countryName];
    });

    const allData = {
      packageName,
      description,
      duration,
      pricing: {
        defaultPrice,
        countryPrices: finalPrices
      },
      images: {
        banner: bannerPreview,
        feature: featurePreview
      }
    };
    const formData = JSON.stringify(allData, null, 2);

    console.log(formData);
    alert('Form submitted. Check console for JSON data!');

    setPackageName('');
    setDescription('');
    setDuration('');
    setDefaultPrice('');
    setSelectedCountry('');
    setCountryCurrencyValue('');
    setCountryPrices({});
    setBannerPreview(null);
    setFeaturePreview(null);
    // Reset file inputs
    document.getElementById('bannerInput').value = '';
    document.getElementById('featureInput').value = '';
  };




  const Formstyle = "mt-2 flex w-full bg-background border border-input shadow-xs shadow-black/5 transition-[color,box-shadow] text-foreground placeholder:text-muted-foreground/80 placeholder:text-sm focus-visible:ring-ring/30 focus-visible:border-ring focus-visible:outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 [&[readonly]]:opacity-70 file:h-full [&[type=file]]:py-0 file:border-solid file:border-input file:bg-transparent file:font-medium file:not-italic file:text-foreground file:p-0 file:border-0 file:border-e aria-invalid:border-destructive/60 aria-invalid:ring-destructive/10 dark:aria-invalid:border-destructive dark:aria-invalid:ring-destructive/20 h-12 px-3 text-[0.8125rem] leading-(--text-sm--line-height) rounded-md file:pe-3 file:me-3"
  const labelstyle = "text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-50 font-medium text-music-light"

  return (
    <div className="min-h-screen w-4/5 mx-auto py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-full mx-auto">
        <div className="bg-music-background-orange rounded-xl shadow-lg border border-music-light overflow-hidden">
          <div className="px-8 py-6 border-b border-music-light">
            <h2 className="text-4xl font-semibold text-music-light text-center">
              Create a Course
            </h2>
            <p className="mt-1 text-lg text-music-light text-center">
              Fill in the details below to create your course
            </p>
          </div>

          <form className="p-8" onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column */}
              <div className="space-y-4">
                {/* Package Name */}
                <div className="space-y-2">
                  <label htmlFor="packageName" className={labelstyle}>
                    Package Name
                  </label>
                  <input
                    type="text"
                    required
                    id="packageName"
                    value={packageName}
                    onChange={(e) => setPackageName(e.target.value)}
                    className={Formstyle}
                    placeholder="Enter package name"
                  />
                </div>

                {/* Description with Tabs */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <label className={labelstyle}>
                      Description (HTML Allowed)
                    </label>
                    <div className="flex space-x-1 bg-music-background-light p-1 rounded-lg">
                      <button
                        type="button"
                        className={`px-3 py-1.5 text-sm font-medium rounded-md ${
                          activeTab === 'preview'
                            ? 'bg-music-background text-white'
                            : 'text-music-bg-background hover:text-bg-music-light'
                        }`}
                        onClick={() => setActiveTab('preview')}
                      >
                        Preview
                      </button>
                      <button
                        type="button"
                        className={`px-3 py-1.5 text-sm font-medium rounded-md ${
                          activeTab === 'code'
                            ? 'bg-music-background text-white'
                            : 'text-music-bg-background hover:text-bg-music-light'
                        }`}
                        onClick={() => setActiveTab('code')}
                      >
                        Code
                      </button>
                    </div>
                  </div>

                  {activeTab === 'code' ? (
                    <textarea
                      id="description"
                      value={description}
                      required
                      onChange={(e) => setDescription(e.target.value)}
                      className={`${Formstyle} min-h-[12rem] pt-2`}
                      placeholder="Enter course description..."
                    />
                  ) : (
                    <div
                      className="block w-full rounded-lg border bg-background min-h-[12rem] max-h-[16rem] overflow-y-auto p-4 text-sm prose prose-invert max-w-none [&_img]:w-full [&_img]:h-auto"
                      dangerouslySetInnerHTML={{
                        __html: DOMPurify.sanitize(description),
                      }}
                    />
                  )}
                </div>

                {/* Duration */}
                <div className="space-y-2">
                  <label htmlFor="duration" className={labelstyle}>
                    Duration
                  </label>
                  <input
                    type="text"
                    id="duration"
                    value={duration}
                    onChange={(e) => setDuration(e.target.value)}
                    required
                    className={Formstyle}
                    placeholder="e.g. 8 weeks"
                  />
                </div>
                 {/* Pricing Section */}
                 <div className="space-y-2 border border-music-light p-6 rounded-xl">
                  <h3 className="text-lg font-medium text-music-light">Course Pricing</h3>
                  
                  {/* Default Price */}
                  <div className="space-y-2">
                    <label className={labelstyle}>
                      Default Price (USD)
                    </label>
                    <input
                      type="number"
                      required
                      value={defaultPrice}
                      onChange={(e) => setDefaultPrice(e.target.value)}
                      className={Formstyle}
                      placeholder="Enter default price"
                    />
                  </div>

                  {/* Country Prices */}
                  {Object.entries(countryPrices).length > 0 && (
                    <div className="space-y-3">
                      <h4 className={labelstyle}>
                        Custom Prices
                      </h4>
                      <div className="grid gap-2">
                        {Object.entries(countryPrices).map(([country, price]) => {
                          const currency = countries.find(
                            (c) => c.name === country,
                          )?.currency;
                          return (
                            <div
                              key={country}
                              className="flex justify-between items-center bg-background px-4 py-3 rounded-lg border border-input"
                            >
                              <span className="text-sm text-music-background  font-medium ">{country}</span>
                              <span className="text-sm text-music-background font-medium">
                                {currency} {price}
                              </span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}

                  {/* Country Selector */}
                  <div className="space-y-2">
                    <label className={labelstyle}>
                      Add Country-Specific Price
                    </label>
                    <div className="grid grid-cols-2 gap-4">
                      <select
                        value={selectedCountry}
                        onChange={(e) => setSelectedCountry(e.target.value)}
                        className={Formstyle}
                      >
                        <option value="">Select country</option>
                        {countries
                          .filter((c) => !(c.name in countryPrices))
                          .map((c) => (
                            <option key={c.name} value={c.name}>
                              {c.name}
                            </option>
                          ))}
                      </select>

                      {selectedCountry && (
                        <div className="flex gap-2 items-baseline">
                          <input
                            type="number"
                            value={countryCurrencyValue}
                            onChange={(e) => setCountryCurrencyValue(e.target.value)}
                            placeholder={`Price in ${countries.find((c) => c.name === selectedCountry)?.currency}`}
                            className={Formstyle}
                          />
                          <button
                            type="button"
                            onClick={handleAddCountryPrice}
                            className="inline-flex items-center px-4 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-music-background hover:bg-music-background/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-music-background focus:ring-offset-music-background-orange"
                          >
                            Add
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Column */}
              <div className="space-y-4">
               

                {/* Images Section */}
                <div className="space-y-6 border border-music-light p-6 rounded-xl">
                  <h3 className="text-lg font-medium text-music-light">Course Images</h3>
                  <div className="flex justify-between gap-4">
                  {/* Banner Image */}
                  <div className="space-y-2 w-1/2">
                    <label htmlFor="bannerImage" className={labelstyle}>
                      Banner Image
                    </label>
                    <div className="mt-1 flex justify-center py-2  border-2 border-input border-dashed rounded-lg">
                      <div className="space-y-1 text-center">
                        <div className="flex text-sm text-music-light">
                          <label
                            htmlFor="bannerImage"
                            className="relative cursor-pointer rounded-md font-medium text-music-background hover:text-music-background/90 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-music-background focus-within:ring-offset-music-background-orange"
                          >
                            <span>Upload a file</span>
                            <input
                              type="file"
                              id="bannerImage"
                              accept="image/*"
                              onChange={(e) => handleImageChange(e, setBannerPreview)}
                              className="sr-only"
                            />
                          </label>
                      
                        </div>
                        
                      </div>
                    </div>
                    {bannerPreview && (
                      <div className="mt-2">
                        <img
                          src={bannerPreview}
                          alt="Banner Preview"
                          className="w-full h-48 object-cover rounded-lg"
                        />
                        <button
                          type="button"
                          onClick={() => removeImage(setBannerPreview, 'bannerImage')}
                          className="mt-2 text-sm font-medium text-music-light hover:text-music-background-light/90"
                        >
                          Remove Image
                        </button>
                      </div>
                    )}
                  </div>

                  {/* Feature Image */}
                  <div className="space-y-2 w-1/2">
                    <label htmlFor="featureImage" className={labelstyle}>
                      Feature Image
                    </label>
                    <div className="mt-1 flex justify-center py-2  border-2 border-input border-dashed rounded-lg">
                      <div className="space-y-1 text-center">
                        <div className="flex text-sm text-music-light">
                          <label
                            htmlFor="featureImage"
                            className="relative cursor-pointer rounded-md font-medium text-music-background hover:text-music-background/90 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-music-background focus-within:ring-offset-music-background-orange"
                          >
                            <span>Upload a file</span>
                            <input
                              type="file"
                              id="featureImage"
                              accept="image/*"
                              required
                              onChange={(e) => handleImageChange(e, setFeaturePreview)}
                              className="sr-only"
                            />
                          </label>
                        
                        </div>
                       
                      </div>
                    </div>
                    {featurePreview && (
                      <div className="mt-2">
                        <img
                          src={featurePreview}
                          alt="Feature Preview"
                          className="w-full h-48 object-cover rounded-lg"
                        />
                        <button
                          type="button"
                          onClick={() => removeImage(setFeaturePreview, 'featureImage')}
                          className="mt-2 text-sm font-medium text-music-light hover:text-music-background-light/90"
                        >
                          Remove Image
                        </button>
                      </div>
                    )}
                  </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="mt-8 pt-4 border-t border-music-light">
              <button
                type="submit"
                className="text-center mx-auto flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-music-background hover:bg-music-background/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-music-background focus:ring-offset-music-background-orange"
              >
                Create Course
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Course;
